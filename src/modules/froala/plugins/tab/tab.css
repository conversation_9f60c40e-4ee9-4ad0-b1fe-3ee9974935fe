/* Tabs */
.fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel div.tabs p:not(.tab-title) {
    list-style-type: none;
  }
  .fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
  }
  .custom-theme.fr-popup .fr-colors-tabs {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab {
    color: #222222;
    padding: 8px 0;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:hover,
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:focus {
    color: #1e88e5;
  }
  .custom-theme.fr-popup
    .fr-colors-tabs
    .fr-colors-tab[data-param1="background"]::after {
    bottom: 0;
    left: 0;
    background: #1e88e5;
    -webkit-transition: transform none;
    -moz-transition: transform none;
    -ms-transition: transform none;
    -o-transition: transform none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab.fr-selected-tab {
    color: #1e88e5;
  }
  .fr-element.fr-view .tabs-panel .tabs .tab-title {
  list-style-type: circle;
  color: #0067c7;
  margin-bottom: unset;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  /* Create the text field appearance */
  box-shadow: 
    inset 1px 1px 2px rgba(0, 0, 0, 0.1),      /* Top-left inner shadow */
    inset -1px -1px 1px rgba(255, 255, 255, 0.8); /* Bottom-right inner highlight */
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  position: relative;
  outline: none;
}

.fr-element.fr-view .tabs-panel .tabs .tab-title.fr-selected-cell {
  outline: 1px solid #3b82f6;
    outline-offset: -7px;
    border-radius: 6px;
    box-shadow: inset 1px 0px 0px 6px rgba(0, 0, 0, 0.1), inset -1px -1px 1px rgba(255, 255, 255, 0.8), 0 0 0 2px rgba(59, 130, 246, 0.2);
}
  

  .fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
  }

  .removeBtn{
    border: none;
    background: white;
    padding: 9px 4px 4px 4px;
    border-radius: 3px;
  }
  
.fr-element.fr-view .tabs-panel .tabs .tab-title .analytics-pill {
    display: inline-block;
    color: #4a90e2;
    font-weight: bold;
    border: 1px solid #4a90e2;
    background-color: white;
    width: 100%;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    cursor: pointer;
    transition: box-shadow 0.2s ease;
  }
  
  .fr-element.fr-view .tabs-panel .tabs .tab-title .analytics-pill:hover,
  .fr-element.fr-view .tabs-panel .tabs .tab-title .analytics-pill:focus {
    box-shadow: 0 0 0 2px #4a90e2;
    outline: none;
  }

  .fr-element.fr-view .tabs-panel .tabs .fr-selected-cell {
    outline: 1px solid #4a90e2;
    outline-offset: -1px;
    /* background-color: white; */
  }

  .fr-element.fr-view .tabs-panel .tabs .fr-selected-cell .analytics-pill {
    box-shadow: 0 0 0 2px #4a90e2;
    outline: none;
  }

  .fr-element.fr-view table.tabs-panel td.fr-selected-cell::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-color: transparent !important;
  }

  /* Dropdown menu container */
.fr-dropdown-menu {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 4px 0;
  min-width: 160px;
  margin: 0;
}

/* Dropdown items */
.fr-dropdown-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.15s ease;
  border-radius: 0;
}

.fr-dropdown-item:hover {
  background-color: #f3f4f6;
  color: #111827;
}

/* Icon styling */
.fr-dropdown-item .fr-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  opacity: 0.6;
  flex-shrink: 0;
}

.fr-dropdown-item:hover .fr-icon {
  opacity: 0.8;
}

/* Text styling */
.fr-dropdown-item .fr-text {
  flex: 1;
  font-weight: 400;
}

/* Remove previous button styling */
.removeBtn {
  border: none;
  background: transparent;
  padding: 0;
  border-radius: 0;
}

/* Add icons using pseudo-elements if you don't have icon fonts */
.fr-icon-delete-column::before {
  content: "⫶"; /* Or use your preferred icon */
  font-size: 16px;
}

.fr-icon-delete-table::before {
  content: "⊞"; /* Or use your preferred icon */
  font-size: 16px;
}