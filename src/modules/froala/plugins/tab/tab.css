/* Tabs */
.fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel div.tabs p:not(.tab-title) {
    list-style-type: none;
  }
  .fr-element.fr-view .tabs-panel div.tabs p.tab-title {
    list-style-type: circle;
    color: #0067c7;
  }
  .fr-element.fr-view .tabs-panel {
    background: #f5f5f5;
  }
  .custom-theme.fr-popup .fr-colors-tabs {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab {
    color: #222222;
    padding: 8px 0;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:hover,
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab:focus {
    color: #1e88e5;
  }
  .custom-theme.fr-popup
    .fr-colors-tabs
    .fr-colors-tab[data-param1="background"]::after {
    bottom: 0;
    left: 0;
    background: #1e88e5;
    -webkit-transition: transform none;
    -moz-transition: transform none;
    -ms-transition: transform none;
    -o-transition: transform none;
  }
  .custom-theme.fr-popup .fr-colors-tabs .fr-colors-tab.fr-selected-tab {
    color: #1e88e5;
  }
  .fr-element.fr-view .tabs-panel .tabs .tab-title {
    list-style-type: circle;
    color: #0067c7;
    margin-bottom: unset;
    padding: 12px 16px;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    position: relative;
    outline: none;
  }

  .fr-element.fr-view .tabs-panel .tabs .tab-title:hover {
    border-color: #9ca3af;
  }

  .fr-element.fr-view .tabs-panel .tabs .tab-title:focus {
    border-color: #3b82f6;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Add spacing between tab headers */
  .fr-element.fr-view .tabs-panel .tabs td.tab-title:not(:last-child) {
    padding-right: 4px;
  }

  .fr-element.fr-view .tabs-panel .tabs td.tab-title:not(:first-child) {
    padding-left: 4px;
  }
  .fr-element.fr-view .tabs-panel {
    background: #f9fafb;
    padding: 8px;
    border-radius: 8px;
  }

  .removeBtn{
    border: none;
    background: white;
    padding: 9px 4px 4px 4px;
    border-radius: 3px;
  }
  
.fr-element.fr-view .tabs-panel .tabs .tab-title .analytics-pill {
    display: inline-block;
    color: #4a90e2;
    font-weight: bold;
    border: 1px solid #4a90e2;
    background-color: white;
    width: 100%;
    padding: 4px 12px;
    border-radius: 6px;
    font-size: 14px;
    line-height: 1.5;
    cursor: pointer;
    transition: box-shadow 0.2s ease;
  }
  
  .fr-element.fr-view .tabs-panel .tabs .tab-title .analytics-pill:hover,
  .fr-element.fr-view .tabs-panel .tabs .tab-title .analytics-pill:focus {
    box-shadow: 0 0 0 2px #4a90e2;
    outline: none;
  }

  .fr-element.fr-view .tabs-panel .tabs .fr-selected-cell {
    outline: 1px solid #4a90e2;
    outline-offset: -1px;
    /* background-color: white; */
  }

  .fr-element.fr-view .tabs-panel .tabs .fr-selected-cell .analytics-pill {
    box-shadow: 0 0 0 2px #4a90e2;
    outline: none;
  }

  .fr-element.fr-view table.tabs-panel td.fr-selected-cell::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background-color: transparent !important;
  }